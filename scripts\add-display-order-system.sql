-- إضافة نظام الترتيب الاختياري للتمارين والدروس
-- Add optional ordering system for exercises and lessons

-- ===== إضافة حقل display_order لجميع جداول المحتوى =====
-- Add display_order field to all content tables

-- تم إضافة هذه الحقول مسبقاً، لكن نتأكد من وجودها
-- These fields were added previously, but we ensure they exist
ALTER TABLE public.exercises ADD COLUMN IF NOT EXISTS display_order INTEGER;
ALTER TABLE public.homeworks ADD COLUMN IF NOT EXISTS display_order INTEGER;
ALTER TABLE public.exams ADD COLUMN IF NOT EXISTS display_order INTEGER;
ALTER TABLE public.summaries ADD COLUMN IF NOT EXISTS display_order INTEGER;

-- إضافة تعليقات على الحقول الجديدة
COMMENT ON COLUMN public.exercises.display_order IS 'ترتيب عرض التمرين - اختياري (NULL = ترتيب افتراضي)';
COMMENT ON COLUMN public.homeworks.display_order IS 'ترتيب عرض الفرض - اختياري (NULL = ترتيب افتراضي)';
COMMENT ON COLUMN public.exams.display_order IS 'ترتيب عرض الامتحان - اختياري (NULL = ترتيب افتراضي)';
COMMENT ON COLUMN public.summaries.display_order IS 'ترتيب عرض الملخص - اختياري (NULL = ترتيب افتراضي)';

-- ===== إنشاء فهارس للترتيب السريع =====
-- Create indexes for fast ordering

-- فهارس مركبة للترتيب حسب الدرس والترتيب المخصص
CREATE INDEX IF NOT EXISTS exercises_lesson_order_idx ON public.exercises(lesson_id, display_order NULLS LAST, id);
CREATE INDEX IF NOT EXISTS homeworks_lesson_order_idx ON public.homeworks(lesson_id, display_order NULLS LAST, id);
CREATE INDEX IF NOT EXISTS exams_lesson_order_idx ON public.exams(lesson_id, display_order NULLS LAST, id);
CREATE INDEX IF NOT EXISTS summaries_lesson_order_idx ON public.summaries(lesson_id, display_order NULLS LAST, id);

-- ===== دوال مساعدة لإدارة الترتيب =====
-- Helper functions for order management

-- دالة لإعادة ترقيم التمارين تلقائياً
CREATE OR REPLACE FUNCTION auto_reorder_exercises(lesson_id_param TEXT)
RETURNS void AS $$
DECLARE
    exercise_record RECORD;
    counter INTEGER := 1;
BEGIN
    -- إعادة ترقيم التمارين بترتيب ID
    FOR exercise_record IN 
        SELECT id FROM public.exercises 
        WHERE lesson_id = lesson_id_param 
        ORDER BY id
    LOOP
        UPDATE public.exercises 
        SET display_order = counter * 10  -- استخدام مضاعفات 10 لترك مساحة للإدراج
        WHERE id = exercise_record.id;
        
        counter := counter + 1;
    END LOOP;
    
    RAISE NOTICE 'تم إعادة ترقيم % تمرين للدرس %', counter - 1, lesson_id_param;
END;
$$ LANGUAGE plpgsql;

-- دالة لإعادة ترقيم الفروض تلقائياً
CREATE OR REPLACE FUNCTION auto_reorder_homeworks(lesson_id_param TEXT)
RETURNS void AS $$
DECLARE
    homework_record RECORD;
    counter INTEGER := 1;
BEGIN
    FOR homework_record IN 
        SELECT id FROM public.homeworks 
        WHERE lesson_id = lesson_id_param 
        ORDER BY id
    LOOP
        UPDATE public.homeworks 
        SET display_order = counter * 10
        WHERE id = homework_record.id;
        
        counter := counter + 1;
    END LOOP;
    
    RAISE NOTICE 'تم إعادة ترقيم % فرض للدرس %', counter - 1, lesson_id_param;
END;
$$ LANGUAGE plpgsql;

-- دالة لإعادة ترقيم الامتحانات تلقائياً
CREATE OR REPLACE FUNCTION auto_reorder_exams(lesson_id_param TEXT)
RETURNS void AS $$
DECLARE
    exam_record RECORD;
    counter INTEGER := 1;
BEGIN
    FOR exam_record IN 
        SELECT id FROM public.exams 
        WHERE lesson_id = lesson_id_param 
        ORDER BY id
    LOOP
        UPDATE public.exams 
        SET display_order = counter * 10
        WHERE id = exam_record.id;
        
        counter := counter + 1;
    END LOOP;
    
    RAISE NOTICE 'تم إعادة ترقيم % امتحان للدرس %', counter - 1, lesson_id_param;
END;
$$ LANGUAGE plpgsql;

-- دالة لإعادة ترقيم الملخصات تلقائياً
CREATE OR REPLACE FUNCTION auto_reorder_summaries(lesson_id_param TEXT)
RETURNS void AS $$
DECLARE
    summary_record RECORD;
    counter INTEGER := 1;
BEGIN
    FOR summary_record IN 
        SELECT id FROM public.summaries 
        WHERE lesson_id = lesson_id_param 
        ORDER BY id
    LOOP
        UPDATE public.summaries 
        SET display_order = counter * 10
        WHERE id = summary_record.id;
        
        counter := counter + 1;
    END LOOP;
    
    RAISE NOTICE 'تم إعادة ترقيم % ملخص للدرس %', counter - 1, lesson_id_param;
END;
$$ LANGUAGE plpgsql;

-- دالة شاملة لإعادة ترقيم جميع محتويات الدرس
CREATE OR REPLACE FUNCTION auto_reorder_lesson_content(lesson_id_param TEXT)
RETURNS void AS $$
BEGIN
    -- تحديد نوع المحتوى للدرس
    DECLARE
        lesson_content_type TEXT;
    BEGIN
        SELECT content_type INTO lesson_content_type 
        FROM public.lessons 
        WHERE id = lesson_id_param;
        
        IF lesson_content_type IS NULL THEN
            RAISE NOTICE 'لم يتم العثور على الدرس %', lesson_id_param;
            RETURN;
        END IF;
        
        -- إعادة ترقيم حسب نوع المحتوى
        CASE lesson_content_type
            WHEN 'exercise' THEN
                PERFORM auto_reorder_exercises(lesson_id_param);
            WHEN 'homework' THEN
                PERFORM auto_reorder_homeworks(lesson_id_param);
            WHEN 'exam' THEN
                PERFORM auto_reorder_exams(lesson_id_param);
            WHEN 'summary' THEN
                PERFORM auto_reorder_summaries(lesson_id_param);
            ELSE
                RAISE NOTICE 'نوع محتوى غير معروف: %', lesson_content_type;
        END CASE;
    END;
END;
$$ LANGUAGE plpgsql;

-- ===== أمثلة على الاستخدام =====
-- Usage examples

-- مثال 1: تحديد ترتيب مخصص لتمارين معينة
-- Example 1: Set custom order for specific exercises
/*
UPDATE public.exercises 
SET display_order = 5 
WHERE id = 'exercise_id_1';

UPDATE public.exercises 
SET display_order = 10 
WHERE id = 'exercise_id_2';

UPDATE public.exercises 
SET display_order = 15 
WHERE id = 'exercise_id_3';
*/

-- مثال 2: إعادة ترقيم تلقائي لجميع تمارين درس معين
-- Example 2: Auto-reorder all exercises in a lesson
/*
SELECT auto_reorder_exercises('lesson_id_here');
*/

-- مثال 3: إعادة ترقيم جميع محتويات درس (تلقائي حسب النوع)
-- Example 3: Auto-reorder all content in a lesson (automatic by type)
/*
SELECT auto_reorder_lesson_content('lesson_id_here');
*/

-- ===== إحصائيات الترتيب =====
-- Ordering statistics

-- دالة لعرض إحصائيات الترتيب
CREATE OR REPLACE FUNCTION get_ordering_stats()
RETURNS TABLE(
    content_type TEXT,
    total_items BIGINT,
    items_with_order BIGINT,
    items_without_order BIGINT,
    percentage_ordered NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    -- إحصائيات التمارين
    SELECT 
        'exercises'::TEXT,
        COUNT(*) as total_items,
        COUNT(display_order) as items_with_order,
        COUNT(*) - COUNT(display_order) as items_without_order,
        ROUND((COUNT(display_order)::NUMERIC / COUNT(*)) * 100, 2) as percentage_ordered
    FROM public.exercises
    
    UNION ALL
    
    -- إحصائيات الفروض
    SELECT 
        'homeworks'::TEXT,
        COUNT(*) as total_items,
        COUNT(display_order) as items_with_order,
        COUNT(*) - COUNT(display_order) as items_without_order,
        ROUND((COUNT(display_order)::NUMERIC / COUNT(*)) * 100, 2) as percentage_ordered
    FROM public.homeworks
    
    UNION ALL
    
    -- إحصائيات الامتحانات
    SELECT 
        'exams'::TEXT,
        COUNT(*) as total_items,
        COUNT(display_order) as items_with_order,
        COUNT(*) - COUNT(display_order) as items_without_order,
        ROUND((COUNT(display_order)::NUMERIC / COUNT(*)) * 100, 2) as percentage_ordered
    FROM public.exams
    
    UNION ALL
    
    -- إحصائيات الملخصات
    SELECT 
        'summaries'::TEXT,
        COUNT(*) as total_items,
        COUNT(display_order) as items_with_order,
        COUNT(*) - COUNT(display_order) as items_without_order,
        ROUND((COUNT(display_order)::NUMERIC / COUNT(*)) * 100, 2) as percentage_ordered
    FROM public.summaries;
END;
$$ LANGUAGE plpgsql;

-- ===== التحقق من نجاح التحديث =====
-- Verify successful update

-- عرض هيكل الجداول المحدثة
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('exercises', 'homeworks', 'exams', 'summaries')
    AND column_name = 'display_order'
    AND table_schema = 'public'
ORDER BY table_name;

-- عرض إحصائيات الترتيب
SELECT * FROM get_ordering_stats();

-- رسالة نجاح
DO $$
BEGIN
    RAISE NOTICE '✅ تم إضافة نظام الترتيب الاختياري بنجاح!';
    RAISE NOTICE '📊 يمكنك استخدام دالة get_ordering_stats() لعرض الإحصائيات';
    RAISE NOTICE '🔧 يمكنك استخدام auto_reorder_lesson_content(''lesson_id'') لإعادة الترقيم التلقائي';
    RAISE NOTICE '📝 الترتيب اختياري: إذا لم تحدد display_order، سيتم الترتيب حسب ID';
END $$;
