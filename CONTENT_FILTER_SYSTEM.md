# نظام فلتر المحتوى الفارغ

## نظرة عامة

تم تطوير نظام فلتر متقدم لإخفاء التبويبات والخانات الفارغة (دروس، تمارين، فروض، امتحانات) وإظهارها تلقائياً عند إضافة محتوى جديد.

## المكونات الرئيسية

### 1. Hook فلتر المحتوى (`useContentFilter`)

```typescript
// hooks/useContentFilter.ts
const {
  exercises,
  homeworks,
  summaries,
  exams,
  visibleTabs,
  availableTabsCount,
  hasAnyContent,
  getFirstAvailableTab
} = useContentFilter(lessons, isExamLevelAllowed);
```

**الميزات:**
- تصنيف المحتوى حسب النوع تلقائياً
- تحديد التبويبات المرئية بناءً على وجود المحتوى
- حساب عدد التبويبات المتاحة
- إرجاع أول تبويب متاح للتنقل التلقائي

### 2. مكون رسائل المحتوى الفارغ (`EmptyContentMessage`)

```typescript
// للرسالة العامة
<EmptyContentMessage type="general" />

// لرسائل التبويبات
<EmptyTabMessage type="exercises" />
<EmptyTabMessage type="homeworks" />
<EmptyTabMessage type="summaries" />
<EmptyTabMessage type="exams" />
```

**الأنواع المدعومة:**
- `general`: رسالة عامة عندما لا يوجد أي محتوى
- `exercises`: رسالة خاصة بالتمارين
- `homeworks`: رسالة خاصة بالفروض
- `summaries`: رسالة خاصة بالملخصات
- `exams`: رسالة خاصة بالامتحانات

## كيفية عمل النظام

### 1. فلترة التبويبات

```typescript
// تحديد التبويبات المرئية
const visibleTabs = {
  exercises: exercises.length > 0,
  homeworks: homeworks.length > 0,
  summaries: summaries.length > 0,
  exams: exams.length > 0 && isExamLevelAllowed
};

// عرض التبويبات فقط إذا كان هناك محتوى
{hasAnyContent ? (
  <Tabs>
    {visibleTabs.exercises && <TabsTrigger value="exercises" />}
    {visibleTabs.homeworks && <TabsTrigger value="homeworks" />}
    {visibleTabs.summaries && <TabsTrigger value="summaries" />}
    {visibleTabs.exams && <TabsTrigger value="exams" />}
  </Tabs>
) : (
  <EmptyContentMessage type="general" />
)}
```

### 2. التنقل التلقائي

```typescript
// تحديث التبويب النشط إذا كان غير متاح
useEffect(() => {
  const currentTabVisible = visibleTabs[activeTab];
  if (!currentTabVisible && hasAnyContent) {
    const firstAvailableTab = getFirstAvailableTab();
    setActiveTab(firstAvailableTab);
  }
}, [visibleTabs, activeTab, hasAnyContent]);
```

### 3. عرض المحتوى الفارغ

```typescript
// في كل تبويب
{exercises.length > 0 ? (
  <div className="space-y-6">
    {exercises.map(lesson => renderLessonCard(lesson, 'exercise'))}
  </div>
) : (
  <EmptyTabMessage type="exercises" />
)}
```

## الفوائد

### 1. تحسين تجربة المستخدم
- إخفاء التبويبات الفارغة يقلل من الارتباك
- رسائل واضحة ومفيدة للمحتوى الفارغ
- تنقل تلقائي ذكي للتبويبات المتاحة

### 2. الأداء
- استخدام `useMemo` لتحسين الأداء
- تحديث التبويبات فقط عند تغيير المحتوى
- تقليل عدد العناصر المعروضة في DOM

### 3. قابلية الصيانة
- كود منظم وقابل لإعادة الاستخدام
- فصل منطق الفلترة عن واجهة المستخدم
- سهولة إضافة أنواع محتوى جديدة

## التخصيص

### إضافة نوع محتوى جديد

```typescript
// في useContentFilter.ts
const newContentType = content.filter(item => item.content_type === 'new_type');

const visibleTabs = {
  // ... التبويبات الموجودة
  newType: newContentType.length > 0
};
```

### تخصيص رسائل المحتوى الفارغ

```typescript
// في EmptyContentMessage.tsx
const contentConfig = {
  // ... التكوينات الموجودة
  newType: {
    icon: NewIcon,
    title: 'لا يوجد محتوى جديد',
    description: 'لم يتم إضافة محتوى جديد بعد',
    bgColor: 'bg-custom-50',
    iconColor: 'text-custom-500',
    textColor: 'text-custom-700'
  }
};
```

## الاستخدام في صفحات أخرى

```typescript
// في أي مكون آخر
import { useContentFilter } from '@/hooks/useContentFilter';
import EmptyContentMessage, { EmptyTabMessage } from '@/components/EmptyContentMessage';

function MyComponent({ content }) {
  const { hasAnyContent, visibleTabs } = useContentFilter(content);
  
  return (
    <div>
      {hasAnyContent ? (
        // عرض المحتوى
      ) : (
        <EmptyContentMessage type="general" />
      )}
    </div>
  );
}
```

## ملاحظات مهمة

1. **التوافق مع الكود الموجود**: النظام يدعم الأنواع القديمة للتوافق مع الكود الموجود
2. **الاستجابة**: التصميم متجاوب ويعمل على جميع الأجهزة
3. **إمكانية الوصول**: يدعم اتجاه النص العربي (RTL)
4. **الأداء**: محسن للأداء باستخدام React hooks المناسبة

## المستقبل

- إضافة إعدادات مخصصة للمستخدم
- دعم الفلترة المتقدمة
- إضافة إحصائيات المحتوى
- تحسينات إضافية للأداء
