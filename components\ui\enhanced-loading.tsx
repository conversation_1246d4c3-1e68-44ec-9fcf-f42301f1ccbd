import React from 'react'
import { Loader2, BookOpen, GraduationCap, FileText, Users } from 'lucide-react'

interface EnhancedLoadingProps {
  type?: 'page' | 'content' | 'data' | 'upload' | 'custom'
  message?: string
  showProgress?: boolean
  progress?: number
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function EnhancedLoading({
  type = 'content',
  message,
  showProgress = false,
  progress = 0,
  className = '',
  size = 'md'
}: EnhancedLoadingProps) {
  const getLoadingConfig = () => {
    switch (type) {
      case 'page':
        return {
          icon: GraduationCap,
          message: message || 'جاري تحميل الصفحة...',
          bgColor: 'from-blue-50 via-white to-indigo-50',
          iconColor: 'text-blue-600',
          showAnimatedIcons: true
        }
      case 'content':
        return {
          icon: BookOpen,
          message: message || 'جاري تحميل المحتوى...',
          bgColor: 'from-green-50 via-white to-emerald-50',
          iconColor: 'text-green-600',
          showAnimatedIcons: true
        }
      case 'data':
        return {
          icon: FileText,
          message: message || 'جاري جلب البيانات...',
          bgColor: 'from-purple-50 via-white to-violet-50',
          iconColor: 'text-purple-600',
          showAnimatedIcons: false
        }
      case 'upload':
        return {
          icon: Users,
          message: message || 'جاري الرفع...',
          bgColor: 'from-orange-50 via-white to-amber-50',
          iconColor: 'text-orange-600',
          showAnimatedIcons: false
        }
      default:
        return {
          icon: Loader2,
          message: message || 'جاري التحميل...',
          bgColor: 'from-gray-50 via-white to-slate-50',
          iconColor: 'text-gray-600',
          showAnimatedIcons: false
        }
    }
  }

  const getSizeConfig = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'py-8',
          icon: 'h-8 w-8',
          mainIcon: 'h-12 w-12',
          text: 'text-sm',
          title: 'text-lg'
        }
      case 'lg':
        return {
          container: 'py-20',
          icon: 'h-8 w-8',
          mainIcon: 'h-20 w-20',
          text: 'text-xl',
          title: 'text-3xl'
        }
      default:
        return {
          container: 'py-16',
          icon: 'h-6 w-6',
          mainIcon: 'h-16 w-16',
          text: 'text-base',
          title: 'text-2xl'
        }
    }
  }

  const config = getLoadingConfig()
  const sizeConfig = getSizeConfig()
  const Icon = config.icon

  return (
    <div className={`bg-gradient-to-br ${config.bgColor} flex items-center justify-center px-4 ${className}`} dir="rtl">
      <div className={`text-center error-page-container ${sizeConfig.container}`}>
        {/* الأيقونة الرئيسية */}
        <div className="relative mb-8 error-icon">
          <div className="mx-auto bg-white rounded-full flex items-center justify-center mb-6 shadow-lg loading-pulse" 
               style={{ width: size === 'lg' ? '6rem' : size === 'sm' ? '3rem' : '4rem', 
                       height: size === 'lg' ? '6rem' : size === 'sm' ? '3rem' : '4rem' }}>
            <Icon className={`${sizeConfig.mainIcon} ${config.iconColor}`} />
          </div>
          
          {/* دائرة التحميل */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="border-4 border-blue-200 border-t-blue-600 rounded-full loading-spinner"
                 style={{ width: size === 'lg' ? '8rem' : size === 'sm' ? '4rem' : '6rem', 
                         height: size === 'lg' ? '8rem' : size === 'sm' ? '4rem' : '6rem' }}></div>
          </div>
        </div>

        {/* النص */}
        <h2 className={`font-semibold text-gray-800 mb-4 arabic-heading ${sizeConfig.title}`}>
          {config.message}
        </h2>
        
        <p className={`text-gray-600 arabic-text mb-8 ${sizeConfig.text}`}>
          يرجى الانتظار بينما نحضر المحتوى لك
        </p>

        {/* شريط التقدم */}
        {showProgress && (
          <div className="w-64 mx-auto bg-gray-200 rounded-full h-2 mb-6">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300 loading-pulse" 
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            ></div>
          </div>
        )}

        {/* أيقونات متحركة */}
        {config.showAnimatedIcons && (
          <div className="flex justify-center space-x-4 space-x-reverse">
            <BookOpen className={`${sizeConfig.icon} text-blue-400 loading-bounce`} style={{ animationDelay: '0ms' }} />
            <Loader2 className={`${sizeConfig.icon} text-blue-500 loading-spinner`} />
            <GraduationCap className={`${sizeConfig.icon} text-blue-400 loading-bounce`} style={{ animationDelay: '200ms' }} />
          </div>
        )}
      </div>
    </div>
  )
}

// مكون تحميل مبسط
export function SimpleLoading({ 
  message = "جاري التحميل...",
  size = 'md' 
}: { 
  message?: string
  size?: 'sm' | 'md' | 'lg' 
}) {
  const sizeConfig = {
    sm: { spinner: 'h-4 w-4', text: 'text-sm' },
    md: { spinner: 'h-6 w-6', text: 'text-base' },
    lg: { spinner: 'h-8 w-8', text: 'text-lg' }
  }[size]

  return (
    <div className="flex items-center justify-center py-8" dir="rtl">
      <div className="text-center">
        <Loader2 className={`${sizeConfig.spinner} animate-spin text-blue-600 mx-auto mb-3`} />
        <p className={`text-gray-600 arabic-text ${sizeConfig.text}`}>{message}</p>
      </div>
    </div>
  )
}

// مكون تحميل للأزرار
export function ButtonLoading({ 
  children, 
  loading = false,
  className = "",
  ...props 
}: { 
  children: React.ReactNode
  loading?: boolean
  className?: string
  [key: string]: any
}) {
  return (
    <button 
      className={`inline-flex items-center ${className}`} 
      disabled={loading}
      {...props}
    >
      {loading && <Loader2 className="h-4 w-4 animate-spin ml-2" />}
      {children}
    </button>
  )
}

// مكون تحميل للبطاقات
export function CardLoading({ count = 3 }: { count?: number }) {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
          <div className="h-4 bg-gray-200 rounded mb-4"></div>
          <div className="h-3 bg-gray-200 rounded mb-2"></div>
          <div className="h-3 bg-gray-200 rounded mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
        </div>
      ))}
    </div>
  )
}

export default EnhancedLoading
