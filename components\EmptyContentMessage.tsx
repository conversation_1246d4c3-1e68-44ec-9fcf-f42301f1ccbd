'use client'

import Link from 'next/link'
import { ArrowLeft, Construction, Clock, BookOpen, Home, FileText, GraduationCap, AlertCircle } from 'lucide-react'

interface EmptyContentMessageProps {
  type: 'lesson' | 'homework' | 'summary' | 'exam' | 'general' | 'exercises' | 'homeworks' | 'summaries' | 'exams'
  title?: string
  description?: string
  subjectId?: string
  returnTo?: string
  showTip?: boolean
  className?: string
}

const contentConfig = {
  general: {
    icon: BookOpen,
    title: 'المحتوى قيد التطوير',
    description: 'نعمل حالياً على إضافة محتوى تعليمي شامل لهذه المادة. سيتم إضافة الدروس والتمارين والفروض والامتحانات قريباً.',
    bgColor: 'bg-muted/30',
    iconColor: 'text-muted-foreground',
    textColor: 'text-muted-foreground'
  },
  exercises: {
    icon: BookOpen,
    title: 'لا توجد تمارين',
    description: 'لم يتم إضافة تمارين لهذه المادة بعد',
    bgColor: 'bg-blue-50',
    iconColor: 'text-blue-500',
    textColor: 'text-blue-700'
  },
  homeworks: {
    icon: Home,
    title: 'لا توجد فروض',
    description: 'لم يتم إضافة فروض لهذه المادة بعد',
    bgColor: 'bg-orange-50',
    iconColor: 'text-orange-500',
    textColor: 'text-orange-700'
  },
  summaries: {
    icon: FileText,
    title: 'لا توجد ملخصات',
    description: 'لم يتم إضافة ملخصات دروس لهذه المادة بعد',
    bgColor: 'bg-green-50',
    iconColor: 'text-green-500',
    textColor: 'text-green-700'
  },
  exams: {
    icon: GraduationCap,
    title: 'لا توجد امتحانات',
    description: 'لم يتم إضافة امتحانات لهذه المادة بعد',
    bgColor: 'bg-purple-50',
    iconColor: 'text-purple-500',
    textColor: 'text-purple-700'
  },
  // الأنواع القديمة للتوافق مع الكود الموجود
  lesson: {
    icon: Construction,
    title: 'الدرس قيد التطوير',
    description: 'سيتم إضافة التمارين والأنشطة التفاعلية قريباً',
    bgColor: 'bg-orange-50',
    iconColor: 'text-orange-400',
    textColor: 'text-gray-800'
  },
  homework: {
    icon: Construction,
    title: 'الفرض المنزلي قيد التطوير',
    description: 'سيتم إضافة الفروض والتمارين المنزلية قريباً',
    bgColor: 'bg-orange-50',
    iconColor: 'text-orange-400',
    textColor: 'text-gray-800'
  },
  summary: {
    icon: Construction,
    title: 'الملخص قيد التطوير',
    description: 'سيتم إضافة الملخص والنقاط المهمة قريباً',
    bgColor: 'bg-orange-50',
    iconColor: 'text-orange-400',
    textColor: 'text-gray-800'
  },
  exam: {
    icon: Construction,
    title: 'الامتحان قيد التطوير',
    description: 'سيتم إضافة أسئلة الامتحان والتقييم قريباً',
    bgColor: 'bg-orange-50',
    iconColor: 'text-orange-400',
    textColor: 'text-gray-800'
  }
};

export default function EmptyContentMessage({
  type,
  title,
  description,
  subjectId,
  returnTo,
  showTip = true,
  className = ''
}: EmptyContentMessageProps) {
  const config = contentConfig[type] || contentConfig.general;
  const Icon = config.icon;

  const displayTitle = title || config.title;
  const displayDescription = description || config.description;

  // للأنواع القديمة، استخدم التصميم القديم
  if (['lesson', 'homework', 'summary', 'exam'].includes(type)) {
    return (
      <div className="text-center py-16 px-4">
        <div className="max-w-md mx-auto">
          <div className="relative mb-6">
            <Construction className="h-20 w-20 text-orange-400 mx-auto mb-2" />
            <Clock className="h-8 w-8 text-orange-300 absolute -top-1 -right-1 animate-pulse" />
          </div>

          <h2 className="text-2xl font-bold text-gray-800 mb-3">
            {displayTitle}
          </h2>

          {title && (
            <h3 className="text-lg font-semibold text-gray-600 mb-4">
              {title}
            </h3>
          )}

          <p className="text-gray-500 mb-6 leading-relaxed">
            {displayDescription}
          </p>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
            <p className="text-orange-700 text-sm">
              <strong>ملاحظة:</strong> نحن نعمل بجد لإضافة محتوى عالي الجودة.
              تابعنا للحصول على التحديثات الجديدة!
            </p>
          </div>

          <div className="space-y-3">
            {subjectId && (
              <Link
                href={returnTo || `/subject/${subjectId}`}
                className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة إلى المادة
              </Link>
            )}

            <div>
              <Link
                href="/levels"
                className="inline-flex items-center px-4 py-2 text-primary hover:text-primary/80 transition-colors"
              >
                تصفح المراحل الدراسية
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // للأنواع الجديدة، استخدم التصميم الجديد
  return (
    <div className={`text-center py-16 ${className}`} dir="rtl">
      <div className={`${config.bgColor} rounded-xl p-8 max-w-md mx-auto`}>
        <Icon className={`h-20 w-20 ${config.iconColor} mx-auto mb-6`} />
        <h3 className={`text-2xl font-bold mb-4 arabic-heading ${config.textColor}`}>
          {displayTitle}
        </h3>
        <p className={`${config.textColor} arabic-text leading-relaxed`}>
          {displayDescription}
        </p>

        {showTip && type === 'general' && (
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 justify-center mb-2">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <span className="text-blue-700 text-sm font-medium arabic-text">نصيحة</span>
            </div>
            <p className="text-blue-700 text-sm arabic-text">
              تابع الموقع للحصول على آخر التحديثات والمحتوى الجديد
            </p>
          </div>
        )}

        {showTip && type !== 'general' && ['exercises', 'homeworks', 'summaries', 'exams'].includes(type) && (
          <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <p className="text-gray-600 text-xs arabic-text">
              سيتم إضافة المحتوى قريباً
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

// مكون مبسط لعرض رسالة فارغة في التبويبات
export function EmptyTabMessage({
  type,
  title,
  description
}: {
  type: 'exercises' | 'homeworks' | 'summaries' | 'exams';
  title?: string;
  description?: string;
}) {
  const config = contentConfig[type];
  const Icon = config.icon;

  return (
    <div className="text-center py-12" dir="rtl">
      <Icon className={`h-16 w-16 ${config.iconColor} mx-auto mb-4`} />
      <h3 className={`text-xl font-semibold mb-2 arabic-heading ${config.textColor}`}>
        {title || config.title}
      </h3>
      <p className={`${config.textColor} arabic-text`}>
        {description || config.description}
      </p>
    </div>
  );
}
