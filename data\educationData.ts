// Re-export everything from the backend API
export * from '@/backend/api/educationAPI';

// Re-export specific functions from dataLoader that are not in educationAPI
export {
  getLevels,
  getYears,
  getSubjects,
  getLessons,
  getLessonsWithExercisesForSubject
} from '@/backend/utils/dataLoader';

// Export the raw data types for backward compatibility
export * from './types';

// Export needed data for components that expect synchronous access
import { levels, getLevels, getYears, getSubjects, getLessons } from '@/backend/utils/dataLoader';
export const educationLevels = levels;

// Alias functions for sitemap generation
export const getAllLevels = getLevels;
export const getAllYears = getYears;
export const getAllSubjects = getSubjects;
export const getAllLessons = getLessons;
