import { useMemo } from 'react';

export interface ContentItem {
  id: string;
  content_type?: 'exercise' | 'homework' | 'summary' | 'exam';
  [key: string]: any;
}

export interface ContentFilterResult {
  exercises: ContentItem[];
  homeworks: ContentItem[];
  summaries: ContentItem[];
  exams: ContentItem[];
  visibleTabs: {
    exercises: boolean;
    homeworks: boolean;
    summaries: boolean;
    exams: boolean;
  };
  availableTabsCount: number;
  hasAnyContent: boolean;
  getFirstAvailableTab: () => string;
}

/**
 * Hook لفلترة المحتوى وإدارة التبويبات المرئية
 * يخفي التبويبات الفارغة ويظهرها عند إضافة محتوى
 */
export function useContentFilter(
  content: ContentItem[] = [],
  isExamLevelAllowed: boolean = true
): ContentFilterResult {
  return useMemo(() => {
    // تصنيف المحتوى حسب النوع مع التعامل مع القيم غير المحددة
    const exercises = content.filter(item => item.content_type === 'exercise');
    const homeworks = content.filter(item => item.content_type === 'homework');
    const summaries = content.filter(item => item.content_type === 'summary');
    const exams = content.filter(item => item.content_type === 'exam');

    // تحديد التبويبات المرئية (التي تحتوي على محتوى)
    const visibleTabs = {
      exercises: exercises.length > 0,
      homeworks: homeworks.length > 0,
      summaries: summaries.length > 0,
      exams: exams.length > 0 && isExamLevelAllowed
    };

    // حساب عدد التبويبات المتاحة
    const availableTabsCount = Object.values(visibleTabs).filter(Boolean).length;
    
    // فحص وجود أي محتوى
    const hasAnyContent = availableTabsCount > 0;

    // دالة للحصول على أول تبويب متاح
    const getFirstAvailableTab = (): string => {
      if (visibleTabs.exercises) return 'exercises';
      if (visibleTabs.homeworks) return 'homeworks';
      if (visibleTabs.summaries) return 'summaries';
      if (visibleTabs.exams) return 'exams';
      return 'exercises'; // fallback
    };

    return {
      exercises,
      homeworks,
      summaries,
      exams,
      visibleTabs,
      availableTabsCount,
      hasAnyContent,
      getFirstAvailableTab
    };
  }, [content, isExamLevelAllowed]);
}

/**
 * Hook مبسط لفحص ما إذا كان المحتوى فارغ
 */
export function useIsContentEmpty(content: ContentItem[] = []): boolean {
  return useMemo(() => {
    return !content || content.length === 0;
  }, [content]);
}

/**
 * Hook لفحص ما إذا كان نوع محتوى معين فارغ
 */
export function useIsContentTypeEmpty(
  content: ContentItem[] = [],
  contentType: ContentItem['content_type']
): boolean {
  return useMemo(() => {
    const filteredContent = content.filter(item => item.content_type === contentType);
    return filteredContent.length === 0;
  }, [content, contentType]);
}

/**
 * Hook لإحصائيات المحتوى
 */
export function useContentStats(content: ContentItem[] = []) {
  return useMemo(() => {
    const stats = {
      total: content.length,
      exercises: content.filter(item => item.content_type === 'exercise').length,
      homeworks: content.filter(item => item.content_type === 'homework').length,
      summaries: content.filter(item => item.content_type === 'summary').length,
      exams: content.filter(item => item.content_type === 'exam').length,
    };

    return {
      ...stats,
      isEmpty: stats.total === 0,
      hasExercises: stats.exercises > 0,
      hasHomeworks: stats.homeworks > 0,
      hasSummaries: stats.summaries > 0,
      hasExams: stats.exams > 0,
    };
  }, [content]);
}
