import { useQuery, useQueries, useQueryClient } from '@tanstack/react-query'
import { useMemo, useCallback } from 'react'
import {
  getLevels,
  getYearsForLevel,
  getSubjectsForYear,
  getLessonsForSubject,
  getLevel,
  getSubject,
  getYear,
  getLesson
} from '@/data/educationData'
import type { Level, Year, Subject, Lesson } from '@/data/types'

// Performance optimization: Prefetch related data
export function usePrefetchEducationData() {
  const queryClient = useQueryClient()

  const prefetchYearsForLevel = useCallback((levelId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.years(levelId),
      queryFn: () => getYearsForLevel(levelId),
      staleTime: 30 * 60 * 1000,
    })
  }, [queryClient])

  const prefetchSubjectsForYear = useCallback((yearId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.subjects(yearId),
      queryFn: () => getSubjectsForYear(yearId),
      staleTime: 30 * 60 * 1000,
    })
  }, [queryClient])

  const prefetchLessonsForSubject = useCallback((subjectId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.lessons(subjectId),
      queryFn: () => getLessonsForSubject(subjectId),
      staleTime: 30 * 60 * 1000,
    })
  }, [queryClient])

  return {
    prefetchYearsForLevel,
    prefetchSubjectsForYear,
    prefetchLessonsForSubject
  }
}

// مفاتيح الاستعلام للتخزين المؤقت
export const queryKeys = {
  levels: ['levels'] as const,
  years: (levelId?: string) => levelId ? ['years', levelId] : ['years'] as const,
  subjects: (yearId?: string) => yearId ? ['subjects', yearId] : ['subjects'] as const,
  lessons: (subjectId?: string) => subjectId ? ['lessons', subjectId] : ['lessons'] as const,
  level: (id: string) => ['level', id] as const,
  year: (id: string) => ['year', id] as const,
  subject: (id: string) => ['subject', id] as const,
  lesson: (id: string) => ['lesson', id] as const,
}

// Hook لجلب جميع المستويات
export function useLevels() {
  return useQuery({
    queryKey: queryKeys.levels,
    queryFn: getLevels,
    staleTime: 30 * 60 * 1000, // 30 دقائق - البيانات نادراً ما تتغير
  })
}

// Hook لجلب السنوات حسب المستوى
export function useYearsForLevel(levelId: string) {
  return useQuery({
    queryKey: queryKeys.years(levelId),
    queryFn: () => getYearsForLevel(levelId),
    enabled: !!levelId,
    staleTime: 30 * 60 * 1000, // 30 دقائق
  })
}

// Hook لجلب المواد حسب السنة
export function useSubjectsForYear(yearId: string) {
  return useQuery({
    queryKey: queryKeys.subjects(yearId),
    queryFn: () => getSubjectsForYear(yearId),
    enabled: !!yearId,
    staleTime: 30 * 60 * 1000, // 30 دقائق
  })
}

// Hook لجلب الدروس حسب المادة (بدون التمارين للأداء الأفضل)
export function useLessonsForSubject(subjectId: string) {
  return useQuery({
    queryKey: queryKeys.lessons(subjectId),
    queryFn: () => getLessonsForSubject(subjectId),
    enabled: !!subjectId,
    staleTime: 30 * 60 * 1000, // 30 دقائق - قد تتغير أكثر
  })
}

// Hook لجلب الدروس مع التمارين عند الحاجة فقط
export function useLessonsWithExercisesForSubject(subjectId: string) {
  return useQuery({
    queryKey: [...queryKeys.lessons(subjectId), 'with-exercises'],
    queryFn: () => {
      const { getLessonsWithExercisesForSubject } = require('@/data/educationData');
      return getLessonsWithExercisesForSubject(subjectId);
    },
    enabled: !!subjectId,
    staleTime: 30 * 60 * 1000, // 30 دقائق
  })
}

// Hook لجلب مستوى واحد
export function useLevel(levelId: string) {
  return useQuery({
    queryKey: queryKeys.level(levelId),
    queryFn: () => getLevel(levelId),
    enabled: !!levelId,
    staleTime: 30 * 60 * 1000,
  })
}

// Hook لجلب سنة واحدة
export function useYear(yearId: string) {
  return useQuery({
    queryKey: queryKeys.year(yearId),
    queryFn: () => getYear(yearId),
    enabled: !!yearId,
    staleTime: 30 * 60 * 1000,
  })
}

// Hook لجلب مادة واحدة
export function useSubject(subjectId: string) {
  return useQuery({
    queryKey: queryKeys.subject(subjectId),
    queryFn: () => getSubject(subjectId),
    enabled: !!subjectId,
    staleTime: 30 * 60 * 1000,
  })
}

// Hook لجلب درس واحد
export function useLesson(lessonId: string) {
  return useQuery({
    queryKey: queryKeys.lesson(lessonId),
    queryFn: () => getLesson(lessonId),
    enabled: !!lessonId,
    staleTime: 30 * 60 * 1000,
  })
}

// Hook محسن لصفحة المستويات - يجلب المستويات والسنوات معاً
export function useLevelsWithYears() {
  const levelsQuery = useLevels()
  
  const yearsQueries = useQueries({
    queries: (levelsQuery.data || []).map(level => ({
      queryKey: queryKeys.years(level.id),
      queryFn: () => getYearsForLevel(level.id),
      enabled: !!levelsQuery.data,
      staleTime: 30 * 60 * 1000,
    }))
  })

  const isLoading = levelsQuery.isLoading || yearsQueries.some(q => q.isLoading)
  const isError = levelsQuery.isError || yearsQueries.some(q => q.isError)

  // تجميع البيانات
  const levelsWithYears = (levelsQuery.data || []).map((level, index) => ({
    ...level,
    years: yearsQueries[index]?.data || []
  }))

  return {
    data: levelsWithYears,
    isLoading,
    isError,
    levels: levelsQuery.data || [],
    refetch: () => {
      levelsQuery.refetch()
      yearsQueries.forEach(q => q.refetch())
    }
  }
}

// Hook محسن لصفحة السنة - يجلب السنة والمواد معاً
export function useYearWithSubjects(yearId: string) {
  const yearQuery = useYear(yearId)
  const subjectsQuery = useSubjectsForYear(yearId)

  return {
    year: yearQuery.data,
    subjects: subjectsQuery.data || [],
    isLoading: yearQuery.isLoading || subjectsQuery.isLoading,
    isError: yearQuery.isError || subjectsQuery.isError,
    refetch: () => {
      yearQuery.refetch()
      subjectsQuery.refetch()
    }
  }
}

// Hook محسن لصفحة المادة - يجلب المادة والسنة والدروس معاً (بدون التمارين للأداء الأفضل)
export function useSubjectWithDetails(subjectId: string) {
  const subjectQuery = useSubject(subjectId)
  const yearQuery = useYear(subjectQuery.data?.yearId || '')
  const lessonsQuery = useLessonsForSubject(subjectId)

  // Memoize the result to prevent unnecessary re-renders
  const result = useMemo(() => ({
    subject: subjectQuery.data,
    year: yearQuery.data,
    lessons: lessonsQuery.data || [],
    isLoading: subjectQuery.isLoading || yearQuery.isLoading || lessonsQuery.isLoading,
    isError: subjectQuery.isError || yearQuery.isError || lessonsQuery.isError,
    refetch: () => {
      subjectQuery.refetch()
      yearQuery.refetch()
      lessonsQuery.refetch()
    }
  }), [subjectQuery, yearQuery, lessonsQuery])

  return result
}

// Hook محسن لصفحة المادة مع التمارين - للاستخدام عند الحاجة فقط
export function useSubjectWithDetailsAndExercises(subjectId: string) {
  const subjectQuery = useSubject(subjectId)
  const yearQuery = useYear(subjectQuery.data?.yearId || '')
  const lessonsQuery = useLessonsWithExercisesForSubject(subjectId)

  // Memoize the result to prevent unnecessary re-renders
  const result = useMemo(() => ({
    subject: subjectQuery.data,
    year: yearQuery.data,
    lessons: lessonsQuery.data || [],
    isLoading: subjectQuery.isLoading || yearQuery.isLoading || lessonsQuery.isLoading,
    isError: subjectQuery.isError || yearQuery.isError || lessonsQuery.isError,
    refetch: () => {
      subjectQuery.refetch()
      yearQuery.refetch()
      lessonsQuery.refetch()
    }
  }), [subjectQuery, yearQuery, lessonsQuery])

  return result
}

// Hook for paginated lessons with virtual scrolling support
export function usePaginatedLessons(subjectId: string, pageSize: number = 20) {
  const lessonsQuery = useLessonsForSubject(subjectId)

  const paginatedData = useMemo(() => {
    const lessons = lessonsQuery.data || []
    const totalPages = Math.ceil(lessons.length / pageSize)

    return {
      lessons,
      totalPages,
      totalCount: lessons.length,
      getPage: (page: number) => {
        const start = (page - 1) * pageSize
        const end = start + pageSize
        return lessons.slice(start, end)
      }
    }
  }, [lessonsQuery.data, pageSize])

  return {
    ...lessonsQuery,
    ...paginatedData
  }
}

// Hook for optimized lesson search and filtering
export function useOptimizedLessonSearch(subjectId: string) {
  const lessonsQuery = useLessonsForSubject(subjectId)

  const searchLessons = useCallback((query: string, contentType?: string) => {
    const lessons = lessonsQuery.data || []

    return lessons.filter(lesson => {
      const matchesQuery = !query ||
        lesson.title.toLowerCase().includes(query.toLowerCase()) ||
        lesson.description?.toLowerCase().includes(query.toLowerCase())

      const matchesType = !contentType || lesson.content_type === contentType

      return matchesQuery && matchesType
    })
  }, [lessonsQuery.data])

  const groupLessonsByType = useCallback(() => {
    const lessons = lessonsQuery.data || []

    return lessons.reduce((acc, lesson) => {
      const type = lesson.content_type || 'exercise'
      if (!acc[type]) acc[type] = []
      acc[type].push(lesson)
      return acc
    }, {} as Record<string, Lesson[]>)
  }, [lessonsQuery.data])

  return {
    ...lessonsQuery,
    searchLessons,
    groupLessonsByType
  }
}
