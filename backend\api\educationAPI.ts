import { Level, Year, Subject, Lesson, Exercise } from '@/data/types';
import {
  getLevels,
  getYears,
  getSubjects,
  getLessons,
  getYearsForLevel as fetchYearsForLevel,
  getSubjectsForYear as fetchSubjectsForYear,
  getLessonsForSubject as fetchLessonsForSubject,
  setDataSource,
  levels
} from '../utils/dataLoader';

// Export the function to set data source
export { setDataSource };

// Also export the raw data for backward compatibility
export { levels };

// وظيفة لمسح التخزين المؤقت وإعادة تحميل البيانات
import { clearAllData } from '../utils/storageManager';

export async function refreshAllData(): Promise<void> {
  // مسح التخزين المؤقت
  clearAllData();

  // مسح الذاكرة المؤقتة
  levelsCache = null;
  yearsCache = null;
  subjectsCache = null;
  lessonsCache = null;

  // إعادة تحميل البيانات الأساسية
  console.log("جاري إعادة تحميل البيانات...");
  await Promise.all([
    loadLevels(),
    loadYears(),
    loadSubjects(),
    loadLessons()
  ]);
  console.log("تم إعادة تحميل البيانات بنجاح");
}

// Cache for loaded data
let levelsCache: Level[] | null = null;
let yearsCache: Year[] | null = null;
let subjectsCache: Subject[] | null = null;
let lessonsCache: Lesson[] | null = null;

// Helper functions to load and cache data
async function loadLevels(): Promise<Level[]> {
  if (!levelsCache) {
    levelsCache = await getLevels();
    console.log(`تم تحميل ${levelsCache.length} مستويات دراسية`);
  }
  return levelsCache;
}

async function loadYears(): Promise<Year[]> {
  if (!yearsCache) {
    yearsCache = await getYears();
    console.log(`تم تحميل ${yearsCache.length} سنوات دراسية`);
  }
  return yearsCache;
}

async function loadSubjects(): Promise<Subject[]> {
  if (!subjectsCache) {
    subjectsCache = await getSubjects();
    console.log(`تم تحميل ${subjectsCache.length} مواد دراسية`);
  }
  return subjectsCache;
}

async function loadLessons(): Promise<Lesson[]> {
  if (!lessonsCache) {
    lessonsCache = await getLessons();
    console.log(`تم تحميل ${lessonsCache.length} دروس`);

    // عرض تفاصيل عن كل درس وتمارينه
    for (const lesson of lessonsCache) {
      console.log(`الدرس: ${lesson.title} - عدد التمارين: ${lesson.exercises.length}`);
    }
  }
  return lessonsCache;
}

// Functions to get full data objects with their relations
export async function getLevel(levelId: string): Promise<Level | undefined> {
  // استخدام الدالة المحسنة لجلب مستوى واحد فقط
  const { getSingleLevel } = await import('@/backend/utils/dataLoader');
  const level = await getSingleLevel(levelId);

  return level || undefined;
}

export async function getYear(yearId: string): Promise<Year | undefined> {
  // استخدام الدالة المحسنة لجلب سنة واحدة فقط
  const { getSingleYear } = await import('@/backend/utils/dataLoader');
  const year = await getSingleYear(yearId);

  return year || undefined;
}

export async function getSubject(subjectId: string): Promise<Subject | undefined> {
  // استخدام الدالة المحسنة لجلب مادة واحدة فقط
  const { getSingleSubject } = await import('@/backend/utils/dataLoader');
  const subject = await getSingleSubject(subjectId);

  return subject || undefined;
}

export async function getLesson(lessonId: string): Promise<Lesson | undefined> {
  // استخدام الدالة المحسنة لجلب درس واحد فقط
  const { getSingleLesson } = await import('@/backend/utils/dataLoader');
  const lesson = await getSingleLesson(lessonId);

  return lesson || undefined;
}

// دوال خاصة للاستخدام في بيئة الخادم (Server-Side) - تتجنب localStorage
import {
  getYearsServerSide,
  getSubjectsServerSide,
  getLevelsServerSide,
  getLessonsServerSide,
  getSingleLessonServerSide,
  getSingleSubjectServerSide,
  getSingleYearServerSide,
  getSingleLevelServerSide
} from '@/backend/utils/dataLoader';

export async function getYearServerSide(yearId: string): Promise<Year | undefined> {
  try {
    // استخدام الدالة المحسنة لجلب سنة واحدة فقط
    const year = await getSingleYearServerSide(yearId);
    return year || undefined;
  } catch (error) {
    console.error(`خطأ في جلب السنة ${yearId} (Server-Side):`, error);
    return undefined;
  }
}

export async function getSubjectServerSide(subjectId: string): Promise<Subject | undefined> {
  try {
    // استخدام الدالة المحسنة لجلب مادة واحدة فقط
    const subject = await getSingleSubjectServerSide(subjectId);
    return subject || undefined;
  } catch (error) {
    console.error(`خطأ في جلب المادة ${subjectId} (Server-Side):`, error);
    return undefined;
  }
}

export async function getLevelServerSide(levelId: string): Promise<Level | undefined> {
  try {
    // استخدام الدالة المحسنة لجلب مستوى واحد فقط
    const level = await getSingleLevelServerSide(levelId);
    return level || undefined;
  } catch (error) {
    console.error(`خطأ في جلب المستوى ${levelId} (Server-Side):`, error);
    return undefined;
  }
}

export async function getLessonServerSide(lessonId: string): Promise<Lesson | undefined> {
  try {
    // استخدام الدالة المحسنة لجلب درس واحد فقط
    const lesson = await getSingleLessonServerSide(lessonId);
    return lesson || undefined;
  } catch (error) {
    console.error(`خطأ في جلب الدرس ${lessonId} (Server-Side):`, error);
    return undefined;
  }
}

export async function getExercise(exerciseId: string): Promise<Exercise | undefined> {
  const lessons = await loadLessons();
  for (const lesson of lessons) {
    const exercise = lesson.exercises.find(ex => ex.id === exerciseId);
    if (exercise) {
      return exercise;
    }
  }
  return undefined;
}

// Functions to get related data
export async function getYearsForLevel(levelId: string): Promise<Year[]> {
  try {
    // استخدام الوظيفة المحسنة من dataLoader لجلب السنوات حسب المستوى
    const years = await getYears();
    const filteredYears = years.filter(year => year.levelId === levelId);

    if (filteredYears.length === 0) {
      // إذا لم نجد سنوات في الذاكرة المؤقتة، نستخدم الوظيفة المباشرة
      return await fetchYearsForLevel(levelId);
    }

    return filteredYears;
  } catch (error) {
    console.error(`خطأ في جلب سنوات المستوى ${levelId}:`, error);
    return [];
  }
}

export async function getSubjectsForYear(yearId: string): Promise<Subject[]> {
  try {
    // استخدام الوظيفة المحسنة من dataLoader لجلب المواد حسب السنة
    const subjects = await getSubjects();
    const filteredSubjects = subjects.filter(subject => subject.yearId === yearId);

    if (filteredSubjects.length === 0) {
      // إذا لم نجد مواد في الذاكرة المؤقتة، نستخدم الوظيفة المباشرة
      return await fetchSubjectsForYear(yearId);
    }

    return filteredSubjects;
  } catch (error) {
    console.error(`خطأ في جلب مواد السنة ${yearId}:`, error);
    return [];
  }
}

export async function getLessonsForSubject(subjectId: string): Promise<Lesson[]> {
  try {
    // استخدام الوظيفة المحسنة من dataLoader لجلب الدروس حسب المادة (بدون التمارين للأداء الأفضل)
    const lessons = await getLessons();
    let filteredLessons = lessons.filter(lesson => lesson.subjectId === subjectId);

    if (filteredLessons.length === 0) {
      // إذا لم نجد دروس في الذاكرة المؤقتة، نستخدم الوظيفة المباشرة
      filteredLessons = await fetchLessonsForSubject(subjectId);
    }

    // تصنيف الدروس حسب نوع المحتوى
    const exercises = filteredLessons.filter(l => l.content_type === 'exercise');
    const homeworks = filteredLessons.filter(l => l.content_type === 'homework');
    const summaries = filteredLessons.filter(l => l.content_type === 'summary');
    const exams = filteredLessons.filter(l => l.content_type === 'exam');

    console.log("Lessons data:", filteredLessons);
    console.log("Exercises:", exercises);
    console.log("Homeworks:", homeworks);
    console.log("Summaries:", summaries);
    console.log("Exams:", exams);

    return filteredLessons;
  } catch (error) {
    console.error(`خطأ في جلب دروس المادة ${subjectId}:`, error);
    return [];
  }
}

// دالة جديدة لجلب الدروس مع التمارين عند الحاجة فقط
export async function getLessonsWithExercisesForSubject(subjectId: string): Promise<Lesson[]> {
  try {
    // استخدام الدالة الجديدة لجلب الدروس مع التمارين
    const { getLessonsWithExercisesForSubject: fetchLessonsWithExercises } = await import('@/backend/utils/dataLoader');
    const filteredLessons = await fetchLessonsWithExercises(subjectId);

    console.log(`تم جلب ${filteredLessons.length} دروس مع التمارين للمادة ${subjectId}`);
    return filteredLessons;
  } catch (error) {
    console.error(`خطأ في جلب دروس المادة ${subjectId} مع التمارين:`, error);
    return [];
  }
}

export async function getExercisesForLesson(lessonId: string): Promise<Exercise[]> {
  const lesson = await getLesson(lessonId);
  return lesson ? lesson.exercises : [];
}

// Export all necessary functions
export {
  getLevels,
  getYears,
  getSubjects,
  getLessons
} from '../utils/dataLoader';
