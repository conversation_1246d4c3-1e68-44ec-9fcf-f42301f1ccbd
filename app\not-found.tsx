'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Home, Search, BookOpen } from 'lucide-react'

export default function NotFound() {
  const [searchQuery, setSearchQuery] = useState('')
  const router = useRouter()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // البحث في المستويات والمواد
      router.push(`/levels?search=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4" dir="rtl">
      <div className="max-w-lg mx-auto text-center">
        {/* رقم الخطأ */}
        <div className="mb-8">
          <h1 className="text-8xl font-bold text-blue-200 mb-4">404</h1>
          <h2 className="text-2xl font-bold text-gray-800 arabic-heading">
            الصفحة غير موجودة
          </h2>
          <p className="text-gray-600 mt-4 arabic-text">
            عذراً، لم نتمكن من العثور على الصفحة التي تبحث عنها
          </p>
        </div>

        {/* مربع البحث */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 arabic-heading">
            ابحث في المحتوى التعليمي
          </h3>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="ابحث عن مستوى، مادة، أو درس..."
                className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent arabic-text"
                dir="rtl"
              />
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <button
              type="submit"
              className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors arabic-text"
            >
              بحث
            </button>
          </form>
        </div>

        {/* أزرار التنقل */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors arabic-text"
          >
            <Home className="h-4 w-4 ml-2" />
            العودة للرئيسية
          </Link>

          <Link
            href="/levels"
            className="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors arabic-text"
          >
            <BookOpen className="h-4 w-4 ml-2" />
            تصفح المستويات
          </Link>
        </div>
      </div>
    </div>
  )
}
