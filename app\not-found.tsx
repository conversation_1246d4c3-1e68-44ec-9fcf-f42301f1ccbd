import Link from 'next/link'
import { ArrowLeft, Home, Search, BookOpen, AlertTriangle } from 'lucide-react'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '404 - الصفحة غير موجودة | منصة التعليم المغربي',
  description: 'عذراً، الصفحة التي تبحث عنها غير موجودة. تصفح المحتوى التعليمي المتاح على منصة التعليم المغربي.',
  robots: { index: false, follow: false },
}

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center px-4" dir="rtl">
      <div className="max-w-2xl mx-auto text-center error-page-container">
        {/* رقم الخطأ */}
        <div className="relative mb-8 error-icon">
          <h1 className="text-9xl font-bold text-blue-100 select-none">404</h1>
          <div className="absolute inset-0 flex items-center justify-center">
            <AlertTriangle className="h-20 w-20 text-blue-500 animate-pulse" />
          </div>
        </div>

        {/* العنوان الرئيسي */}
        <h2 className="text-4xl font-bold text-gray-800 mb-4 arabic-heading">
          الصفحة غير موجودة
        </h2>

        {/* الوصف */}
        <p className="text-xl text-gray-600 mb-8 arabic-text leading-relaxed">
          عذراً، لم نتمكن من العثور على الصفحة التي تبحث عنها. 
          ربما تم نقلها أو حذفها أو أن الرابط غير صحيح.
        </p>

        {/* الاقتراحات */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8 border border-gray-100">
          <h3 className="text-2xl font-semibold text-gray-800 mb-6 arabic-heading">
            ماذا يمكنك أن تفعل؟
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* العودة للرئيسية */}
            <div className="text-center p-4 rounded-xl bg-blue-50 border border-blue-100">
              <Home className="h-12 w-12 text-blue-500 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-800 mb-2 arabic-heading">العودة للرئيسية</h4>
              <p className="text-sm text-gray-600 arabic-text">ابدأ من جديد واستكشف المحتوى</p>
            </div>

            {/* تصفح المستويات */}
            <div className="text-center p-4 rounded-xl bg-green-50 border border-green-100">
              <BookOpen className="h-12 w-12 text-green-500 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-800 mb-2 arabic-heading">تصفح المستويات</h4>
              <p className="text-sm text-gray-600 arabic-text">اختر مستواك الدراسي</p>
            </div>
          </div>
        </div>

        {/* أزرار التنقل */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            href="/"
            className="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 rtl-button error-button"
          >
            <Home className="h-5 w-5 ml-2" />
            العودة للرئيسية
          </Link>

          <Link
            href="/levels"
            className="inline-flex items-center px-8 py-4 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 rtl-button error-button"
          >
            <BookOpen className="h-5 w-5 ml-2" />
            تصفح المستويات
          </Link>
        </div>

        {/* رابط البحث */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-gray-500 arabic-text mb-4">
            أو يمكنك البحث عن ما تريد
          </p>
          <Link
            href="/levels"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors arabic-text"
          >
            <Search className="h-4 w-4 ml-2" />
            ابحث في المحتوى التعليمي
          </Link>
        </div>

        {/* معلومات إضافية */}
        <div className="mt-12 text-center">
          <p className="text-sm text-gray-400 arabic-text">
            إذا كنت تعتقد أن هذا خطأ، يرجى 
            <Link href="/contact" className="text-blue-500 hover:text-blue-600 mx-1">
              التواصل معنا
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
