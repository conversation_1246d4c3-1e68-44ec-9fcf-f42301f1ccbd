import Link from 'next/link'
import { Home, BookOpen } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4" dir="rtl">
      <div className="max-w-lg mx-auto text-center">
        {/* رقم الخطأ */}
        <div className="mb-8">
          <h1 className="text-8xl font-bold text-blue-200 mb-4">404</h1>
          <h2 className="text-2xl font-bold text-gray-800 arabic-heading">
            الصفحة غير موجودة
          </h2>
          <p className="text-gray-600 mt-4 arabic-text">
            عذراً، لم نتمكن من العثور على الصفحة التي تبحث عنها
          </p>
        </div>

        {/* أزرار التنقل */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors arabic-text"
          >
            <Home className="h-4 w-4 ml-2" />
            العودة للرئيسية
          </Link>

          <Link
            href="/levels"
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors arabic-text"
          >
            <BookOpen className="h-4 w-4 ml-2" />
            تصفح المستويات
          </Link>
        </div>
      </div>
    </div>
  )
}
