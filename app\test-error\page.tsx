'use client'

import { useState } from 'react'
import Link from 'next/link'
import { AlertTriangle, Loader2 } from 'lucide-react'

export default function TestErrorPage() {
  const [showError, setShowError] = useState(false)
  const [loading, setLoading] = useState(false)

  const triggerError = () => {
    throw new Error('هذا خطأ تجريبي لاختبار صفحة الخطأ العامة')
  }

  const simulateLoading = () => {
    setLoading(true)
    setTimeout(() => setLoading(false), 2000)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4" dir="rtl">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 arabic-text">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (showError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4" dir="rtl">
        <div className="max-w-lg mx-auto text-center">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-800 mb-4 arabic-heading">
            خطأ تجريبي
          </h2>
          <p className="text-gray-600 mb-6 arabic-text">
            هذا مثال على صفحة خطأ مبسطة
          </p>
          <button
            onClick={() => setShowError(false)}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors arabic-text"
          >
            العودة للاختبار
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4" dir="rtl">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-8">
          <h1 className="text-2xl font-bold text-gray-800 mb-6 arabic-heading text-center">
            اختبار صفحات الخطأ
          </h1>

          <div className="space-y-4">
            <button
              onClick={() => setShowError(true)}
              className="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors arabic-text"
            >
              عرض صفحة خطأ مبسطة
            </button>

            <button
              onClick={simulateLoading}
              className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors arabic-text"
            >
              عرض صفحة تحميل
            </button>

            <button
              onClick={triggerError}
              className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors arabic-text"
            >
              تشغيل خطأ حقيقي
            </button>
          </div>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="text-lg font-semibold text-gray-700 mb-4 arabic-heading">
              روابط اختبار
            </h3>
            <div className="space-y-2">
              <Link
                href="/non-existent-page"
                className="block px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors arabic-text"
              >
                صفحة غير موجودة (404)
              </Link>
              <Link
                href="/"
                className="block px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors arabic-text"
              >
                العودة للرئيسية
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
