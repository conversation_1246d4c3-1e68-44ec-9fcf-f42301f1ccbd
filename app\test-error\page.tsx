'use client'

import { useState } from 'react'
import { EnhancedError } from '@/components/ui/enhanced-error'
import { EnhancedLoading } from '@/components/ui/enhanced-loading'
import { ButtonLoading } from '@/components/ui/enhanced-loading'

export default function TestErrorPage() {
  const [showError, setShowError] = useState<string | null>(null)
  const [showLoading, setShowLoading] = useState(false)
  const [buttonLoading, setButtonLoading] = useState(false)

  const triggerError = () => {
    throw new Error('هذا خطأ تجريبي لاختبار صفحة الخطأ العامة')
  }

  const simulateLoading = () => {
    setShowLoading(true)
    setTimeout(() => setShowLoading(false), 3000)
  }

  const simulateButtonLoading = () => {
    setButtonLoading(true)
    setTimeout(() => setButtonLoading(false), 2000)
  }

  if (showLoading) {
    return (
      <EnhancedLoading
        type="content"
        message="جاري تحميل البيانات التجريبية..."
        showProgress={true}
        progress={75}
      />
    )
  }

  if (showError) {
    return (
      <EnhancedError
        type={showError as any}
        onRetry={() => setShowError(null)}
        showBack={true}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4" dir="rtl">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-8 arabic-heading text-center">
            اختبار صفحات الخطأ والتحميل
          </h1>

          <div className="grid md:grid-cols-2 gap-8">
            {/* اختبار أنواع الأخطاء */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-700 mb-4 arabic-heading">
                اختبار أنواع الأخطاء
              </h2>
              
              <button
                onClick={() => setShowError('404')}
                className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors arabic-text"
              >
                خطأ 404 - الصفحة غير موجودة
              </button>

              <button
                onClick={() => setShowError('500')}
                className="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors arabic-text"
              >
                خطأ 500 - خطأ في الخادم
              </button>

              <button
                onClick={() => setShowError('network')}
                className="w-full px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors arabic-text"
              >
                خطأ شبكة - مشكلة في الاتصال
              </button>

              <button
                onClick={() => setShowError('permission')}
                className="w-full px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors arabic-text"
              >
                خطأ صلاحية - غير مسموح بالوصول
              </button>

              <button
                onClick={triggerError}
                className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors arabic-text"
              >
                تشغيل خطأ حقيقي (Error Boundary)
              </button>
            </div>

            {/* اختبار التحميل */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-700 mb-4 arabic-heading">
                اختبار التحميل
              </h2>
              
              <button
                onClick={simulateLoading}
                className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors arabic-text"
              >
                تحميل محتوى (3 ثواني)
              </button>

              <ButtonLoading
                loading={buttonLoading}
                onClick={simulateButtonLoading}
                className="w-full px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors arabic-text"
              >
                زر مع تحميل
              </ButtonLoading>

              <div className="bg-gray-100 rounded-lg p-4">
                <h3 className="font-semibold text-gray-700 mb-2 arabic-heading">
                  معلومات الاختبار
                </h3>
                <ul className="text-sm text-gray-600 space-y-1 arabic-text">
                  <li>• جميع الأخطاء قابلة للإلغاء</li>
                  <li>• التحميل يتوقف تلقائياً</li>
                  <li>• الأنيميشن محسن للأداء</li>
                  <li>• التصميم متجاوب</li>
                </ul>
              </div>
            </div>
          </div>

          {/* روابط مفيدة */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <h3 className="text-lg font-semibold text-gray-700 mb-4 arabic-heading">
              روابط اختبار إضافية
            </h3>
            <div className="flex flex-wrap gap-4">
              <a
                href="/non-existent-page"
                className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors arabic-text"
              >
                صفحة غير موجودة
              </a>
              <a
                href="/"
                className="px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors arabic-text"
              >
                العودة للرئيسية
              </a>
              <a
                href="/levels"
                className="px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors arabic-text"
              >
                تصفح المستويات
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
