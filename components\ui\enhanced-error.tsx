'use client'

import React from 'react'
import Link from 'next/link'
import { Alert<PERSON><PERSON>gle, RefreshCw, Home, ArrowLeft, Search, BookOpen } from 'lucide-react'

interface EnhancedErrorProps {
  type?: '404' | '500' | 'network' | 'permission' | 'custom'
  title?: string
  message?: string
  showRetry?: boolean
  showHome?: boolean
  showBack?: boolean
  onRetry?: () => void
  className?: string
}

export function EnhancedError({
  type = 'custom',
  title,
  message,
  showRetry = true,
  showHome = true,
  showBack = false,
  onRetry,
  className = ''
}: EnhancedErrorProps) {
  const getErrorConfig = () => {
    switch (type) {
      case '404':
        return {
          title: title || 'الصفحة غير موجودة',
          message: message || 'عذراً، لم نتمكن من العثور على الصفحة التي تبحث عنها.',
          bgColor: 'from-blue-50 via-white to-indigo-50',
          iconColor: 'text-blue-500',
          primaryColor: 'bg-blue-600 hover:bg-blue-700'
        }
      case '500':
        return {
          title: title || 'خطأ في الخادم',
          message: message || 'نعتذر، حدث خطأ غير متوقع. نحن نعمل على إصلاح هذه المشكلة.',
          bgColor: 'from-red-50 via-white to-orange-50',
          iconColor: 'text-red-500',
          primaryColor: 'bg-red-600 hover:bg-red-700'
        }
      case 'network':
        return {
          title: title || 'مشكلة في الاتصال',
          message: message || 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى.',
          bgColor: 'from-orange-50 via-white to-yellow-50',
          iconColor: 'text-orange-500',
          primaryColor: 'bg-orange-600 hover:bg-orange-700'
        }
      case 'permission':
        return {
          title: title || 'غير مسموح بالوصول',
          message: message || 'ليس لديك صلاحية للوصول إلى هذا المحتوى.',
          bgColor: 'from-purple-50 via-white to-pink-50',
          iconColor: 'text-purple-500',
          primaryColor: 'bg-purple-600 hover:bg-purple-700'
        }
      default:
        return {
          title: title || 'حدث خطأ',
          message: message || 'نعتذر، حدث خطأ غير متوقع.',
          bgColor: 'from-gray-50 via-white to-slate-50',
          iconColor: 'text-gray-500',
          primaryColor: 'bg-gray-600 hover:bg-gray-700'
        }
    }
  }

  const config = getErrorConfig()

  return (
    <div className={`min-h-[400px] bg-gradient-to-br ${config.bgColor} flex items-center justify-center px-4 ${className}`} dir="rtl">
      <div className="max-w-lg mx-auto text-center error-page-container">
        {/* أيقونة الخطأ */}
        <div className="relative mb-8 error-icon">
          <div className="w-24 h-24 mx-auto bg-white rounded-full flex items-center justify-center mb-6 shadow-lg">
            <AlertTriangle className={`h-12 w-12 ${config.iconColor} animate-pulse`} />
          </div>
        </div>

        {/* العنوان */}
        <h2 className="text-3xl font-bold text-gray-800 mb-4 arabic-heading">
          {config.title}
        </h2>

        {/* الرسالة */}
        <p className="text-lg text-gray-600 mb-8 arabic-text leading-relaxed">
          {config.message}
        </p>

        {/* أزرار العمل */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          {showRetry && onRetry && (
            <button
              onClick={onRetry}
              className={`inline-flex items-center px-6 py-3 ${config.primaryColor} text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 rtl-button error-button`}
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              إعادة المحاولة
            </button>
          )}

          {showBack && (
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 rtl-button error-button"
            >
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للخلف
            </button>
          )}

          {showHome && (
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 rtl-button error-button"
            >
              <Home className="h-4 w-4 ml-2" />
              العودة للرئيسية
            </Link>
          )}
        </div>

        {/* روابط إضافية */}
        {type === '404' && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-gray-500 arabic-text mb-4">
              أو يمكنك تصفح المحتوى التعليمي
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link
                href="/levels"
                className="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors arabic-text"
              >
                <BookOpen className="h-4 w-4 ml-2" />
                تصفح المستويات
              </Link>
              <Link
                href="/levels"
                className="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors arabic-text"
              >
                <Search className="h-4 w-4 ml-2" />
                البحث في المحتوى
              </Link>
            </div>
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-400 arabic-text">
            إذا استمرت المشكلة، يرجى 
            <Link href="/contact" className="text-blue-500 hover:text-blue-600 mx-1">
              التواصل معنا
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

// مكون مبسط للاستخدام السريع
export function QuickError({ 
  message = "حدث خطأ غير متوقع",
  onRetry 
}: { 
  message?: string
  onRetry?: () => void 
}) {
  return (
    <div className="text-center py-8 px-4" dir="rtl">
      <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
      <p className="text-gray-600 arabic-text mb-4">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors rtl-button"
        >
          <RefreshCw className="h-4 w-4 ml-2" />
          إعادة المحاولة
        </button>
      )}
    </div>
  )
}

export default EnhancedError
