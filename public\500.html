<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - خطأ في الخادم | منصة التعليم المغربي</title>
    <meta name="description" content="نعتذر، حدث خطأ في الخادم.">
    <meta name="robots" content="noindex, nofollow">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background-color: #fef2f2;
            color: #374151;
            line-height: 1.6;
        }

        .container {
            max-width: 500px;
            margin: 0 auto;
            text-align: center;
            padding-top: 10vh;
        }

        .error-number {
            font-size: 6rem;
            font-weight: bold;
            color: #fecaca;
            margin-bottom: 1rem;
        }

        .error-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .error-description {
            color: #6b7280;
            margin-bottom: 2rem;
        }

        .button {
            display: inline-block;
            padding: 12px 24px;
            margin: 8px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background-color 0.3s;
            cursor: pointer;
            border: none;
            font-family: inherit;
        }

        .button-primary {
            background-color: #dc2626;
            color: white;
        }

        .button-primary:hover {
            background-color: #b91c1c;
        }

        .button-secondary {
            background-color: #10b981;
            color: white;
        }

        .button-secondary:hover {
            background-color: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-number">500</div>
        <h1 class="error-title">خطأ في الخادم</h1>
        <p class="error-description">
            نعتذر، حدث خطأ غير متوقع. نحن نعمل على إصلاح هذه المشكلة
        </p>

        <div>
            <button onclick="window.location.reload()" class="button button-primary">إعادة المحاولة</button>
            <a href="/" class="button button-secondary">العودة للرئيسية</a>
        </div>
    </div>
</body>
</html>
