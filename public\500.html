<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - خطأ في الخادم | منصة التعليم المغربي</title>
    <meta name="description" content="نعتذر، حدث خطأ في الخادم. نحن نعمل على إصلاح هذه المشكلة.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', 'Aria<PERSON>', 'Helvetica', sans-serif;
            direction: rtl;
        }
        
        .arabic-text {
            font-family: '<PERSON><PERSON><PERSON>', 'Arial', 'Helvetica', sans-serif;
            line-height: 1.8;
            letter-spacing: 0.02em;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .arabic-heading {
            font-weight: 700;
            line-height: 1.4;
            margin-bottom: 0;
            text-align: right;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .error-page-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        .error-icon {
            animation: bounceIn 0.8s ease-out;
        }
        
        .error-button {
            transition: all 0.3s ease;
        }
        
        .error-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50">
    <div class="min-h-screen flex items-center justify-center px-4">
        <div class="max-w-2xl mx-auto text-center error-page-container">
            <!-- رقم الخطأ -->
            <div class="relative mb-8 error-icon">
                <h1 class="text-9xl font-bold text-red-100 select-none">500</h1>
                <div class="absolute inset-0 flex items-center justify-center">
                    <svg class="h-20 w-20 text-red-500 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- العنوان الرئيسي -->
            <h2 class="text-4xl font-bold text-gray-800 mb-4 arabic-heading">
                خطأ في الخادم
            </h2>

            <!-- الوصف -->
            <p class="text-xl text-gray-600 mb-8 arabic-text leading-relaxed">
                نعتذر، حدث خطأ غير متوقع في الخادم. 
                فريقنا التقني يعمل على إصلاح هذه المشكلة.
            </p>

            <!-- الحلول المقترحة -->
            <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 border border-gray-100">
                <h3 class="text-2xl font-semibold text-gray-800 mb-6 arabic-heading">
                    جرب هذه الحلول
                </h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <!-- إعادة المحاولة -->
                    <div class="text-center p-4 rounded-xl bg-blue-50 border border-blue-100">
                        <svg class="h-12 w-12 text-blue-500 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <h4 class="font-semibold text-gray-800 mb-2 arabic-heading">إعادة المحاولة</h4>
                        <p class="text-sm text-gray-600 arabic-text">قد تكون مشكلة مؤقتة</p>
                    </div>

                    <!-- العودة للرئيسية -->
                    <div class="text-center p-4 rounded-xl bg-green-50 border border-green-100">
                        <svg class="h-12 w-12 text-green-500 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        <h4 class="font-semibold text-gray-800 mb-2 arabic-heading">العودة للرئيسية</h4>
                        <p class="text-sm text-gray-600 arabic-text">ابدأ من جديد</p>
                    </div>
                </div>
            </div>

            <!-- أزرار العمل -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button onclick="window.location.reload()" class="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl error-button">
                    <svg class="h-5 w-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    إعادة المحاولة
                </button>

                <a href="/" class="inline-flex items-center px-8 py-4 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl error-button">
                    <svg class="h-5 w-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    العودة للرئيسية
                </a>
            </div>

            <!-- معلومات إضافية -->
            <div class="mt-12 text-center">
                <p class="text-sm text-gray-400 arabic-text">
                    إذا استمرت المشكلة، يرجى 
                    <a href="/contact" class="text-blue-500 hover:text-blue-600 mx-1">
                        التواصل معنا
                    </a>
                    وذكر ما كنت تحاول فعله
                </p>
                
                <div class="mt-4 text-xs text-gray-300">
                    <p>رمز الخطأ: 500 - خطأ داخلي في الخادم</p>
                    <p id="timestamp"></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // عرض الوقت الحالي
        document.getElementById('timestamp').textContent = 'الوقت: ' + new Date().toLocaleString('ar-MA');
        
        // إعادة تحميل تلقائية بعد 30 ثانية
        setTimeout(function() {
            if (confirm('هل تريد إعادة تحميل الصفحة تلقائياً؟')) {
                window.location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
