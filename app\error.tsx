'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { RefreshCw, Home, AlertCircle, Bug } from 'lucide-react'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // تسجيل الخطأ في وحدة التحكم للمطورين
    console.error('خطأ في التطبيق:', error)
  }, [error])

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center px-4" dir="rtl">
      <div className="max-w-2xl mx-auto text-center error-page-container">
        {/* أيقونة الخطأ */}
        <div className="relative mb-8 error-icon">
          <div className="w-32 h-32 mx-auto bg-red-100 rounded-full flex items-center justify-center mb-6">
            <AlertCircle className="h-16 w-16 text-red-500 animate-pulse" />
          </div>
        </div>

        {/* العنوان الرئيسي */}
        <h1 className="text-4xl font-bold text-gray-800 mb-4 arabic-heading">
          حدث خطأ غير متوقع
        </h1>

        {/* الوصف */}
        <p className="text-xl text-gray-600 mb-8 arabic-text leading-relaxed">
          نعتذر، حدث خطأ أثناء تحميل هذه الصفحة. 
          نحن نعمل على إصلاح هذه المشكلة.
        </p>

        {/* معلومات الخطأ للمطورين */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-gray-100 rounded-xl p-6 mb-8 text-left">
            <div className="flex items-center mb-3">
              <Bug className="h-5 w-5 text-gray-600 mr-2" />
              <h3 className="font-semibold text-gray-800">معلومات الخطأ (وضع التطوير)</h3>
            </div>
            <pre className="text-sm text-gray-700 overflow-auto">
              {error.message}
            </pre>
            {error.digest && (
              <p className="text-xs text-gray-500 mt-2">
                معرف الخطأ: {error.digest}
              </p>
            )}
          </div>
        )}

        {/* الحلول المقترحة */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8 border border-gray-100">
          <h3 className="text-2xl font-semibold text-gray-800 mb-6 arabic-heading">
            جرب هذه الحلول
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* إعادة المحاولة */}
            <div className="text-center p-4 rounded-xl bg-blue-50 border border-blue-100">
              <RefreshCw className="h-12 w-12 text-blue-500 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-800 mb-2 arabic-heading">إعادة المحاولة</h4>
              <p className="text-sm text-gray-600 arabic-text">قد تكون مشكلة مؤقتة</p>
            </div>

            {/* العودة للرئيسية */}
            <div className="text-center p-4 rounded-xl bg-green-50 border border-green-100">
              <Home className="h-12 w-12 text-green-500 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-800 mb-2 arabic-heading">العودة للرئيسية</h4>
              <p className="text-sm text-gray-600 arabic-text">ابدأ من جديد</p>
            </div>
          </div>
        </div>

        {/* أزرار العمل */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button
            onClick={reset}
            className="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 rtl-button error-button"
          >
            <RefreshCw className="h-5 w-5 ml-2" />
            إعادة المحاولة
          </button>

          <Link
            href="/"
            className="inline-flex items-center px-8 py-4 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 rtl-button error-button"
          >
            <Home className="h-5 w-5 ml-2" />
            العودة للرئيسية
          </Link>
        </div>

        {/* معلومات إضافية */}
        <div className="mt-12 text-center">
          <p className="text-sm text-gray-400 arabic-text">
            إذا استمرت المشكلة، يرجى 
            <Link href="/contact" className="text-blue-500 hover:text-blue-600 mx-1">
              التواصل معنا
            </Link>
            وذكر ما كنت تحاول فعله
          </p>
        </div>
      </div>
    </div>
  )
}
