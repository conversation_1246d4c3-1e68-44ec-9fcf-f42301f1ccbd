import { Loader2, BookOpen, GraduationCap } from 'lucide-react'

export default function Loading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center px-4" dir="rtl">
      <div className="text-center error-page-container">
        {/* شعار متحرك */}
        <div className="relative mb-8 error-icon">
          <div className="w-24 h-24 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-6 loading-pulse">
            <GraduationCap className="h-12 w-12 text-blue-600" />
          </div>

          {/* دوائر متحركة */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-32 h-32 border-4 border-blue-200 border-t-blue-600 rounded-full loading-spinner"></div>
          </div>
        </div>

        {/* النص */}
        <h2 className="text-2xl font-semibold text-gray-800 mb-4 arabic-heading">
          جاري التحميل...
        </h2>
        
        <p className="text-gray-600 arabic-text mb-8">
          يرجى الانتظار بينما نحضر المحتوى التعليمي لك
        </p>

        {/* شريط التقدم المتحرك */}
        <div className="w-64 mx-auto bg-gray-200 rounded-full h-2 mb-6">
          <div className="bg-blue-600 h-2 rounded-full loading-pulse" style={{ width: '60%' }}></div>
        </div>

        {/* أيقونات متحركة */}
        <div className="flex justify-center space-x-4 space-x-reverse">
          <BookOpen className="h-6 w-6 text-blue-400 loading-bounce" style={{ animationDelay: '0ms' }} />
          <Loader2 className="h-6 w-6 text-blue-500 loading-spinner" />
          <GraduationCap className="h-6 w-6 text-blue-400 loading-bounce" style={{ animationDelay: '200ms' }} />
        </div>
      </div>
    </div>
  )
}
