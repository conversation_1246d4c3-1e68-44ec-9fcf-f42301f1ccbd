<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - الصفحة غير موجودة | منصة التعليم المغربي</title>
    <meta name="description" content="عذراً، الصفحة التي تبحث عنها غير موجودة. تصفح المحتوى التعليمي المتاح على منصة التعليم المغربي.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', 'Arial', 'Helve<PERSON>', sans-serif;
            direction: rtl;
        }
        
        .arabic-text {
            font-family: '<PERSON><PERSON><PERSON>', 'Arial', 'Helvetica', sans-serif;
            line-height: 1.8;
            letter-spacing: 0.02em;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .arabic-heading {
            font-weight: 700;
            line-height: 1.4;
            margin-bottom: 0;
            text-align: right;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .error-page-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        .error-icon {
            animation: bounceIn 0.8s ease-out;
        }
        
        .error-button {
            transition: all 0.3s ease;
        }
        
        .error-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
    <div class="min-h-screen flex items-center justify-center px-4">
        <div class="max-w-2xl mx-auto text-center error-page-container">
            <!-- رقم الخطأ -->
            <div class="relative mb-8 error-icon">
                <h1 class="text-9xl font-bold text-blue-100 select-none">404</h1>
                <div class="absolute inset-0 flex items-center justify-center">
                    <svg class="h-20 w-20 text-blue-500 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
            </div>

            <!-- العنوان الرئيسي -->
            <h2 class="text-4xl font-bold text-gray-800 mb-4 arabic-heading">
                الصفحة غير موجودة
            </h2>

            <!-- الوصف -->
            <p class="text-xl text-gray-600 mb-8 arabic-text leading-relaxed">
                عذراً، لم نتمكن من العثور على الصفحة التي تبحث عنها. 
                ربما تم نقلها أو حذفها أو أن الرابط غير صحيح.
            </p>

            <!-- الاقتراحات -->
            <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 border border-gray-100">
                <h3 class="text-2xl font-semibold text-gray-800 mb-6 arabic-heading">
                    ماذا يمكنك أن تفعل؟
                </h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <!-- العودة للرئيسية -->
                    <div class="text-center p-4 rounded-xl bg-blue-50 border border-blue-100">
                        <svg class="h-12 w-12 text-blue-500 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        <h4 class="font-semibold text-gray-800 mb-2 arabic-heading">العودة للرئيسية</h4>
                        <p class="text-sm text-gray-600 arabic-text">ابدأ من جديد واستكشف المحتوى</p>
                    </div>

                    <!-- تصفح المستويات -->
                    <div class="text-center p-4 rounded-xl bg-green-50 border border-green-100">
                        <svg class="h-12 w-12 text-green-500 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h4 class="font-semibold text-gray-800 mb-2 arabic-heading">تصفح المستويات</h4>
                        <p class="text-sm text-gray-600 arabic-text">اختر مستواك الدراسي</p>
                    </div>
                </div>
            </div>

            <!-- أزرار التنقل -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="/" class="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl error-button">
                    <svg class="h-5 w-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    العودة للرئيسية
                </a>

                <a href="/levels" class="inline-flex items-center px-8 py-4 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl error-button">
                    <svg class="h-5 w-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    تصفح المستويات
                </a>
            </div>

            <!-- رابط البحث -->
            <div class="mt-8 pt-8 border-t border-gray-200">
                <p class="text-gray-500 arabic-text mb-4">
                    أو يمكنك البحث عن ما تريد
                </p>
                <a href="/levels" class="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors arabic-text">
                    <svg class="h-4 w-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    ابحث في المحتوى التعليمي
                </a>
            </div>

            <!-- معلومات إضافية -->
            <div class="mt-12 text-center">
                <p class="text-sm text-gray-400 arabic-text">
                    إذا كنت تعتقد أن هذا خطأ، يرجى 
                    <a href="/contact" class="text-blue-500 hover:text-blue-600 mx-1">
                        التواصل معنا
                    </a>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
