import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// قائمة بالصفحات التي يجب فحصها للمحتوى الفارغ
const CONTENT_PAGES = ['/lesson/', '/homework/', '/summary/', '/exam/']

// خريطة إعادة التوجيه للروابط القديمة
const REDIRECT_MAP: Record<string, string> = {
  'grade1': '1ap',
  'grade2': '2ap',
  'grade3': '3ap',
  'grade4': '4ap',
  'grade5': '5ap',
  'grade6': '6ap',
  'grade7': '1ac',
  'grade8': '2ac',
  'grade9': '3ac',
  'grade10': 'tc',
  'grade11': '1bac',
  'grade12': '2bac'
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // إعادة التوجيه للروابط القديمة
  if (pathname.startsWith('/year/grade')) {
    const segments = pathname.split('/')
    const oldGradeId = segments[2] // grade1, grade2, etc.

    if (REDIRECT_MAP[oldGradeId]) {
      const newPath = pathname.replace(`/year/${oldGradeId}`, `/year/${REDIRECT_MAP[oldGradeId]}`)
      return NextResponse.redirect(new URL(newPath, request.url), 301)
    }
  }

  // فحص إذا كان المسار يحتوي على صفحة محتوى
  const isContentPage = CONTENT_PAGES.some(page => pathname.startsWith(page))

  if (isContentPage) {
    // إضافة header لمنع الأرشفة للصفحات الفارغة
    const response = NextResponse.next()

    // إضافة meta tag لمنع الأرشفة إذا كانت الصفحة فارغة
    response.headers.set('X-Content-Check', 'true')

    return response
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/lesson/:path*',
    '/homework/:path*',
    '/summary/:path*',
    '/exam/:path*',
    '/year/grade:path*'
  ]
}
