'use client'

import React, { Component, ReactNode } from 'react'
import Link from 'next/link'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  showDetails?: boolean
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.setState({ error, errorInfo })
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center px-4" dir="rtl">
          <div className="max-w-2xl mx-auto text-center">
            {/* أيقونة الخطأ */}
            <div className="relative mb-8">
              <div className="w-32 h-32 mx-auto bg-red-100 rounded-full flex items-center justify-center mb-6">
                <AlertTriangle className="h-16 w-16 text-red-500 animate-pulse" />
              </div>
            </div>

            {/* العنوان الرئيسي */}
            <h1 className="text-4xl font-bold text-gray-800 mb-4 arabic-heading">
              حدث خطأ في التطبيق
            </h1>

            {/* الوصف */}
            <p className="text-xl text-gray-600 mb-8 arabic-text leading-relaxed">
              نعتذر، حدث خطأ غير متوقع. نحن نعمل على إصلاح هذه المشكلة.
            </p>

            {/* معلومات الخطأ للمطورين */}
            {(this.props.showDetails || process.env.NODE_ENV === 'development') && this.state.error && (
              <div className="bg-gray-100 rounded-xl p-6 mb-8 text-left">
                <div className="flex items-center mb-3">
                  <Bug className="h-5 w-5 text-gray-600 mr-2" />
                  <h3 className="font-semibold text-gray-800">معلومات الخطأ</h3>
                </div>
                <pre className="text-sm text-gray-700 overflow-auto whitespace-pre-wrap">
                  {this.state.error.message}
                </pre>
                {this.state.error.stack && (
                  <details className="mt-4">
                    <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                      عرض تفاصيل إضافية
                    </summary>
                    <pre className="text-xs text-gray-600 mt-2 overflow-auto whitespace-pre-wrap">
                      {this.state.error.stack}
                    </pre>
                  </details>
                )}
              </div>
            )}

            {/* أزرار العمل */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                onClick={this.handleReset}
                className="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 rtl-button"
              >
                <RefreshCw className="h-5 w-5 ml-2" />
                إعادة المحاولة
              </button>

              <Link
                href="/"
                className="inline-flex items-center px-8 py-4 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 rtl-button"
              >
                <Home className="h-5 w-5 ml-2" />
                العودة للرئيسية
              </Link>
            </div>

            {/* معلومات إضافية */}
            <div className="mt-12 text-center">
              <p className="text-sm text-gray-400 arabic-text">
                إذا استمرت المشكلة، يرجى 
                <Link href="/contact" className="text-blue-500 hover:text-blue-600 mx-1">
                  التواصل معنا
                </Link>
                مع وصف ما كنت تحاول فعله
              </p>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// مكون خطأ مبسط للاستخدام السريع
export function SimpleErrorMessage({ 
  title = "حدث خطأ", 
  message = "نعتذر، حدث خطأ غير متوقع",
  onRetry,
  showHomeButton = true 
}: {
  title?: string
  message?: string
  onRetry?: () => void
  showHomeButton?: boolean
}) {
  return (
    <div className="text-center py-16 px-4" dir="rtl">
      <div className="max-w-md mx-auto">
        <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-6" />
        <h3 className="text-2xl font-bold text-gray-800 mb-4 arabic-heading">
          {title}
        </h3>
        <p className="text-gray-600 arabic-text mb-8 leading-relaxed">
          {message}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {onRetry && (
            <button
              onClick={onRetry}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors rtl-button"
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              إعادة المحاولة
            </button>
          )}
          
          {showHomeButton && (
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors rtl-button"
            >
              <Home className="h-4 w-4 ml-2" />
              العودة للرئيسية
            </Link>
          )}
        </div>
      </div>
    </div>
  )
}

export default ErrorBoundary
